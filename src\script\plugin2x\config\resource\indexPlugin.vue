<template>
    <div class="config-resource-container">
        <header class="config-resource-container-header">MCP资源配置</header>
        <div class="config-resource-container-search">
            <SearchBar :form-cols="searchFormCols" :form="searchForm">
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button style="margin-left: auto" type="plain" icon="el-icon-download"
                    >批量导入
                </el-button>
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    @click="addResourceDialogVisible = true"
                    >新增资源</el-button
                >
            </SearchBar>
        </div>
        <main class="config-resource-container-main">
            <CardTable
                v-loading="isLoading"
                :data="resourceTableData"
                :total="total"
                :layout="'total, prev, pager, next, sizes, jumper'"
                :pagination="pagination"
                :updateTable="getTableData"
                :cardConfig="cardConfig"
                @cardEvent="handleCardEvent"
            >
                <template #expandContent="{ cardData, isEdit }">
                    <div class="expand-content">
                        <DocTable
                            :ref="`docTableRef-${cardData.id}`"
                            class="table-content"
                            v-model="resourceDetailTableData[cardData.id]"
                            :config="tableConfig"
                            :isEdit="isEdit"
                            :key="resourceDetailTableKey"
                        />
                    </div>
                </template>
            </CardTable>
        </main>
        <!-- 新增资源弹窗 -->
        <el-dialog
            title="新增资源"
            :visible.sync="addResourceDialogVisible"
            width="940px"
            :close-on-click-modal="false"
            destroy-on-close
            @close="
                addResourceForm = $options.data().addResourceForm;
                addResourceDialogVisible = false;
            "
        >
            <SearchBar
                ref="addResourceFormRef"
                :form-cols="addResourceFormCols"
                :form="addResourceForm"
            >
            </SearchBar>
            <div class="dialog-footer">
                <el-button @click="addResourceDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleAddResource">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import SearchBar from '@/script/components/SearchBar.vue';
import CardTable from '@/script/components/tables/cardTable/index.vue';
import DocTable from '@/script/components/tables/DocTable.vue';
import ConfigResource from '@/script/api/module/config-resource';

const commonMimeTypes = [
    { value: 'text/plain', label: '纯文本 (.txt)' },
    { value: 'application/json', label: 'JSON 文件 (.json)' },
    { value: 'application/pdf', label: 'PDF 文档 (.pdf)' },
    { value: 'image/jpeg', label: 'JPEG 图片 (.jpg, .jpeg)' },
    { value: 'image/png', label: 'PNG 图片 (.png)' },
    { value: 'application/msword', label: 'Word 文档 (.doc)' },
    {
        value: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        label: 'Word 文档 (.docx)'
    },
    { value: 'application/vnd.ms-excel', label: 'Excel 文件 (.xls)' },
    {
        value: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        label: 'Excel 文件 (.xlsx)'
    },
    { value: 'text/csv', label: 'CSV 文件 (.csv)' },
    { value: 'application/vnd.ms-powerpoint', label: 'PowerPoint (.ppt)' },
    {
        value: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        label: 'PowerPoint (.pptx)'
    },
    { value: 'application/zip', label: 'ZIP 压缩包 (.zip)' },
    { value: 'audio/mpeg', label: 'MP3 音频 (.mp3)' },
    { value: 'video/mp4', label: 'MP4 视频 (.mp4)' }
];

export default {
    name: 'ResourceConfig',
    components: {
        SearchBar,
        CardTable,
        DocTable
    },
    data() {
        return {
            isLoading: false,
            // 搜索表单
            searchFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'resourceDescription',
                        label: '资源描述：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-input',
                        prop: 'resourceName',
                        label: '资源名称：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'isValid',
                        label: '状态：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: [
                            {
                                label: '全部',
                                value: ''
                            },
                            {
                                label: '起效',
                                value: 1
                            },
                            {
                                label: '不起效',
                                value: 0
                            }
                        ]
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 12,
                        isShow: true
                    }
                ]
            ],
            searchForm: {
                resourceDescription: '',
                resourceName: '',
                isValid: ''
            },
            // 导入弹窗
            dialogVisible: false,
            dialogType: 'excel', // 'excel' 或 'json'
            // 新增服务弹窗
            addResourceDialogVisible: false,
            addResourceFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'resourceName',
                        label: '资源名称：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'resourceMimeType',
                        label: '资源类型：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请选择, 允许输入自定义类型',
                            clearable: false,
                            filterable: true,
                            'allow-create': true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: commonMimeTypes
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'resourceUri',
                        label: '资源路由路径：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-input',
                        prop: 'resourcePath',
                        label: '资源文件路径：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'resourceDescription',
                        label: '资源描述：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 24,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'resourceAnnotationsAudience',
                        label: '资源适用角色：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: [
                            {
                                label: '用户',
                                value: 'user'
                            },
                            {
                                label: '助手',
                                value: 'assistant'
                            }
                        ]
                    },
                    {
                        type: 'el-input',
                        prop: 'resourceAnnotationsPriority',
                        label: '资源重要程度：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true,
                            type: 'number',
                            min: 0,
                            max: 1,
                            step: 0.1
                        },
                        rules: [
                            { required: true, message: '必填', trigger: 'blur' },
                            {
                                validator: (rule, value, callback) => {
                                    if (isNaN(value)) {
                                        callback(new Error('请输入有效数字'));
                                        return;
                                    }

                                    const truncated = Math.floor(value * 10) / 10 || undefined;

                                    if (value !== truncated) {
                                        this.addResourceForm.resourceAnnotationsPriority =
                                            truncated;
                                        callback();
                                        return;
                                    }

                                    if (value < 0 || value > 1) {
                                        callback(new Error('数值范围为0-1'));
                                    } else {
                                        callback();
                                    }
                                },
                                trigger: ['blur', 'change']
                            }
                        ],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'comment',
                        label: '其他备注：',
                        labelWidth: '150px',
                        attrs: {
                            type: 'textarea',
                            rows: 3,
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: false, message: '必填', trigger: 'blur' }],
                        span: 24,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ]
            ],
            addResourceForm: {
                resourceName: '',
                resourceDescription: '',
                resourceUri: '',
                resourcePath: '',
                resourceMimeType: '',
                resourceAnnotationsAudience: '',
                resourceAnnotationsPriority: undefined,
                comment: ''
            },
            // 资源表格数据
            resourceTableData: [],
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 10
            },
            // 卡片配置
            cardConfig: {
                module: 'resource',
                icon: {
                    0: require('../../../../img/common/resource-inactive.png'),
                    1: require('../../../../img/common/resource-active.png')
                },
                headerHideItem: ['setting', 'tenantKey', 'tag', 'copy'],
                noCardNav: true
            },
            // 资源详情表格配置
            resourceDetailTableKey: 0,
            resourceDetailTableData: {},
            tableConfig: {
                mode: 'normal',
                labelWidth: 150,
                columns: [
                    {
                        label: '资源名称：',
                        prop: 'resourceName',
                        rules: [{ required: true, message: '必填', trigger: 'blur' }]
                    },
                    {
                        label: '资源类型：',
                        prop: 'resourceMimeType',
                        type: 'el-select',
                        attrs: {
                            placeholder: '请选择, 允许输入自定义类型',
                            clearable: false,
                            filterable: true,
                            'allow-create': true
                        },
                        opts: commonMimeTypes,
                        rules: [{ required: true, message: '必填', trigger: 'blur' }]
                    },
                    {
                        label: '资源文件路径：',
                        prop: 'resourcePath',
                        rules: [{ required: true, message: '必填', trigger: 'blur' }]
                    },
                    {
                        label: '资源路由路径：',
                        prop: 'resourceUri',
                        rules: [{ required: true, message: '必填', trigger: 'blur' }]
                    },
                    {
                        label: '资源描述：',
                        prop: 'resourceDescription',
                        singleLine: true,
                        rules: [{ required: true, message: '必填', trigger: 'blur' }]
                    },
                    {
                        label: '资源适用角色：',
                        prop: 'resourceAnnotationsAudience',
                        type: 'el-select',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        opts: [
                            {
                                label: '用户',
                                value: 'user'
                            },
                            {
                                label: '助手',
                                value: 'assistant'
                            }
                        ]
                    },
                    {
                        label: '资源重要程度：',
                        prop: 'resourceAnnotationsPriority',
                        type: 'el-input',
                        attrs: {
                            type: 'number',
                            min: 0,
                            max: 1,
                            step: 0.1
                        },
                        rules: [
                            { required: true, message: '必填', trigger: 'blur' },
                            {
                                validator: (rule, value, callback) => {
                                    if (isNaN(value)) {
                                        callback(new Error('请输入有效数字'));
                                        return;
                                    }

                                    if (value < 0 || value > 1) {
                                        callback(new Error('数值范围为0-1'));
                                    } else {
                                        callback();
                                    }
                                },
                                trigger: ['blur', 'change']
                            }
                        ]
                    },
                    {
                        label: '其他备注：',
                        prop: 'comment',
                        singleLine: true,
                        textarea: true,
                        fit: true
                    }
                ],
                align: 'top',
                pairCount: 2,
                outBorder: false
            }
        };
    },
    created() {
        this.getTableData(this.pagination);
    },
    methods: {
        handleSearch() {
            this.pagination.curPage = 1;
            this.getTableData(this.pagination);
        },
        getTableData({ curPage = 1, pageSize = this.pagination.pageSize }) {
            this.isLoading = true;
            ConfigResource.getResourcePage({
                pageNum: curPage,
                pageSize: pageSize,
                resourceName: '',
                resourceDescription: '',
                isValid: this.searchForm.isValid
            })
                .then((res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.resourceTableData = res.data.list
                            .map((item) => {
                                return {
                                    rawData: item,
                                    id: item.resourceId,
                                    name: item.resourceName,
                                    description: item.resourceDescription,
                                    isValid: item.isValid,
                                    createdTime: item.createdTime,
                                    lastUpdateTime: item.lastUpdateTime
                                };
                            })
                            .sort((a, b) => {
                                return new Date(b.lastUpdateTime) - new Date(a.lastUpdateTime);
                            });
                        this.total = res.data.total;
                    }
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        openImportDialog(val) {
            this.dialogType = val;
            this.dialogVisible = true;
        },
        // 导入成功回调
        handleImportSuccess(data) {
            console.log('导入成功:', data);
            // 处理导入结果数据
            this.$message.success('导入成功');
        },
        handleAddResource() {
            this.$refs.addResourceFormRef.validForm().then((valid) => {
                if (valid) {
                    ConfigResource.addResource(this.addResourceForm).then((res) => {
                        if (res.serviceFlag === 'TRUE') {
                            this.$message.success(res.returnMsg || '新增成功');
                            this.addResourceDialogVisible = false;
                            this.handleSearch();
                        } else {
                            this.$message.error(res.returnMsg || '新增失败');
                        }
                    });
                }
            });
        },
        handleCardEvent(event) {
            const eventMap = {
                // 展开卡片
                expand: () => {
                    ConfigResource.getResourceDetails({ resourceId: event.params.id }).then(
                        (res) => {
                            if (res.serviceFlag === 'TRUE') {
                                this.resourceDetailTableData[event.params.id] = [res.data];
                                this.resourceDetailTableKey++;
                                event.callback(JSON.parse(JSON.stringify(res.data)));
                            } else {
                                this.$message.error(res.returnMsg || '获取资源详情失败');
                            }
                        }
                    );
                },

                // 保存卡片
                save: () => {
                    const docTableRef = this.$refs[`docTableRef-${event.params.resourceId}`];
                    docTableRef.validate().then((valid) => {
                        if (valid) {
                            ConfigResource.updateResource({
                                resourceId: event.params.resourceId,
                                resourceName:
                                    this.resourceDetailTableData[event.params.resourceId][0]
                                        .resourceName,
                                resourceDescription:
                                    this.resourceDetailTableData[event.params.resourceId][0]
                                        .resourceDescription,
                                resourceUri:
                                    this.resourceDetailTableData[event.params.resourceId][0]
                                        .resourceUri,
                                resourcePath:
                                    this.resourceDetailTableData[event.params.resourceId][0]
                                        .resourcePath,
                                resourceMimeType:
                                    this.resourceDetailTableData[event.params.resourceId][0]
                                        .resourceMimeType,
                                resourceAnnotationsAudience:
                                    this.resourceDetailTableData[event.params.resourceId][0]
                                        .resourceAnnotationsAudience,
                                resourceAnnotationsPriority:
                                    this.resourceDetailTableData[event.params.resourceId][0]
                                        .resourceAnnotationsPriority,
                                comment:
                                    this.resourceDetailTableData[event.params.resourceId][0].comment
                            }).then((res) => {
                                event.callback(res.serviceFlag === 'TRUE');
                                this.handleResourceResponse(res, '保存成功', '保存失败');
                            });
                        }
                    });
                },

                // 删除卡片
                delete: () => {
                    ConfigResource.removeResource({
                        resourceIds: [event.params]
                    }).then((res) => {
                        this.handleResourceResponse(res, '删除成功', '删除失败');
                    });
                },

                // 切换卡片状态（有效/无效）
                valid: () => {
                    ConfigResource.changeResourceValid({
                        resourceIds: [event.params.id],
                        isValid: +event.params.isValid
                    }).then((res) => {
                        this.handleResourceResponse(res, '切换成功', '切换失败');
                    });
                }
            };

            eventMap[event.type] && eventMap[event.type]();
        },
        handleResourceResponse(res, successMsg, errorMsg) {
            if (res.serviceFlag === 'TRUE') {
                this.$message.success(res.returnMsg || successMsg);
                this.handleSearch();
            } else {
                this.$message.error(res.returnMsg || errorMsg);
            }
        }
    }
};
</script>

<style scoped lang="less">
.config-resource-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-header {
        height: 1.25rem;
        margin-left: 0.5rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.25rem;
    }
    &-search {
        height: 3.5rem;
        background: rgba(255, 255, 255, 0.96);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0 1rem;
    }
    &-main {
        height: 0;
        flex: 1;
        display: flex;
        gap: 1rem;
    }
    .dialog-footer {
        text-align: right;
        margin-top: 32px;
    }
    .expand-content {
        width: 100%;
        height: fit-content;
        padding: 0 1rem 1rem 1rem;
        .table-content {
            height: 100%;
            overflow: hidden;
            border-radius: 0.25rem;
            border: 0.0625rem solid #d6dae0;
        }
    }
}
</style>
